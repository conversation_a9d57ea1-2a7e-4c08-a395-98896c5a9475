<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import CreateCardAutoBuyPayment from "@/components/CreateCardAutoBuyPaymentV2/CreateCardAutoBuyPaymentV2.vue";
import type { TCardForIssue } from "@/components/CreateCardV2/types";
import {
  type TCardTariffSlug,
  useCardAutoRefillPost,
  useCardsGet,
  usePromoCodeAttachedListGet,
  usePromoCodeAttachPost,
  usePromoCodeCheckGet,
} from "@/composable";
import UIFullScreenModal from "@/components/ui/UIFullScreenModal/UIFullScreenModal.vue";
import { RouteName } from "@/constants/route_name";
import { useI18n } from "vue-i18n";
import CreateCardSuccess from "@/components/CreateCardSuccess/CreateCardSuccess.vue";
import { TPromoCodeResource } from "@/types/api/TPromoCodeResource";
import UITransition from "@/components/ui/UITransition.vue";
import Loader from "@/components/ui/Loader/Loader.vue";

const route = useRoute();
const router = useRouter();
const { t } = useI18n();

type AutobuyParams = {
  account_id?: string;
  type: TCardTariffSlug;
  system?: string;
  code?: string;
  start_balance?: string;
};

enum State {
  AUTOBUY = "autobuy",
  SUCCESS = "success",
}

const viewState = ref<State>(State.AUTOBUY);
const loading = ref(false);

const autobuyParams = computed<AutobuyParams>(() => {
  return {
    account_id: route.query.account_id as string,
    type: route.query.type as TCardTariffSlug,
    system: route.query.system as string,
    code: route.query.code as string,
    start_balance: route.query.start_balance as string,
  };
});

const cardForIssue = ref<TCardForIssue>({
  bin: "",
  type: autobuyParams.value.type,
  startBalance: Number(autobuyParams.value.start_balance),
  minValue: 1,
  accountId: Number(autobuyParams.value.account_id),
  description: "",
  count: 1,
  system: Number(autobuyParams.value.system),
});

const promocode = ref<string | null>(autobuyParams.value.code || null);
const promocodeData = ref<TPromoCodeResource | null>(null);

const enableAutoRefill = async (cardId: number) => {
  await useCardAutoRefillPost({
    card_id: cardId,
    minimum_balance: "50",
    amount_refill: "50",
  });
};

const handleAutoBuy = async () => {
  viewState.value = State.SUCCESS;
  const { data: cards } = await useCardsGet();
  const resultCard = cards.value?.data?.length ? cards.value?.data[0] : null;

  if (resultCard) {
    await enableAutoRefill(resultCard.id);
  }
};

const sendToDashboard = () => {
  router.push({ name: RouteName.DASHBOARD });
};

const checkAndAttachPromocode = async (code: string) => {
  const { data: checkRes } = await usePromoCodeCheckGet(code);
  if (!checkRes.value?.success || !checkRes.value.data) {
    return false;
  }

  const { data: attachRes } = await usePromoCodeAttachPost(code);
  if (!attachRes.value?.success) {
    return false;
  }

  promocodeData.value = checkRes.value.data;
};

onMounted(async () => {
  if (promocode.value?.length) {
    await checkAndAttachPromocode(promocode.value);
  }
  loading.value = false;
});
</script>

<template>
  <div class="proceed-autobuy-view">
    <div>
      <UIFullScreenModal
        :is-open="true"
        :title="t('create-card.modal-title')"
        :can-go-back="false"
        @close="sendToDashboard">
        <template #content>
          <UITransition :name="'fade-slide-down'">
            <Loader v-if="loading" />
          </UITransition>
          <div class="debug">
            <pre>
              autobuyParams: {{ autobuyParams }}
              promocode: {{ promocode }}
              promocodeData: {{ promocodeData }}
              cardForIssue: {{ cardForIssue }}
            </pre>
          </div>
          <div v-if="viewState === State.AUTOBUY">
            <CreateCardAutoBuyPayment
              :card-for-issue="cardForIssue"
              :promo-code-data="promocodeData"
              @auto-buy-success="handleAutoBuy" />
          </div>
          <div v-else-if="viewState === State.SUCCESS">
            <CreateCardSuccess @confirm="sendToDashboard" />
          </div>
        </template>
      </UIFullScreenModal>
    </div>
  </div>
</template>
